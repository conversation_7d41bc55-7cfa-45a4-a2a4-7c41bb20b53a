﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using UNI.Utilities.HttpClientExtension;
using UNI.Utilities.Keycloak.Models;
using UNI.Utilities.Keycloak.Services;

namespace UNI.Utilities.Keycloak.Services.Realm
{
    public class RealmService : ServiceBase, IRealmService
    {
        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="client"></param>
        public RealmService(HttpClient client) : base(client)
        {
        }

        /// <summary>
        /// ctor with default realm
        /// </summary>
        /// <param name="client"></param>
        /// <param name="defaultRealm"></param>
        public RealmService(HttpClient client, string defaultRealm) : base(client, defaultRealm)
        {
        }

        public async Task<IEnumerable<RealmRepresentation>?> GetRealms(CancellationToken cancellationToken = default)
        {
            var rs = await HttpClient.GetAsync<IEnumerable<RealmRepresentation>>(Endpoints.RealmEndpoints.Realms(),
                cancellationToken);
            return rs.Content;
        }

        public async Task<bool> CreateRealm(RealmCreationModel realm, CancellationToken cancellationToken = default)
        {
            var rs = await HttpClient.PostJsonAsync<RealmCreationModel>(Endpoints.RealmEndpoints.Realms(), realm, cancellationToken: cancellationToken);
            return rs.IsSuccessStatusCode;
        }

        public async Task<RealmRepresentation?> GetRealm(string realmName, CancellationToken cancellationToken = default)
        {
            var rs = await HttpClient.GetAsync<RealmRepresentation>(Endpoints.RealmEndpoints.Realms(realmName),
                cancellationToken);
            return rs.Content;
        }
    }
}

using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using UNI.Utilities.HttpClientExtension.Models;

namespace UNI.Utilities.HttpClientExtension
{
    public static class HttpclientDeleteExtension
    {
        public static Task<ResponseMessage<T?>> DeleteAsync<T>(this HttpClient client, string url, CancellationToken cancellationToken = default)
        {
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, new Uri(url, UriKind.Relative));
            return client.RequestAsync<T>(requestMessage, cancellationToken);
        }

        public static Task<ResponseMessage<string?>> DeleteAsync(this HttpClient client, string url, CancellationToken cancellationToken = default)
        {
            return DeleteAsync<string>(client, url, cancellationToken);
        }
    }
}

﻿using System.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.Threading;
using UNI.Utilities.Keycloak.Models;
using UNI.Utilities.Keycloak.Models.Users;

namespace UNI.Utilities.Keycloak.Services.Users;

public interface IUserService
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="briefRepresentation">Boolean which defines whether brief representations are returned (default: false)</param>
    /// <param name="email">A String contained in email, or the complete email, if param "exact" is true</param>
    /// <param name="emailVerified">whether the email has been verified</param>
    /// <param name="enabled"><PERSON><PERSON><PERSON> representing if user is enabled or not</param>
    /// <param name="exact">Boolean which defines whether the params "last", "first", "email" and "username" must match exactly</param>
    /// <param name="first">Pagination offset</param>
    /// <param name="firstName">A String contained in firstName, or the complete firstName, if param "exact" is true</param>
    /// <param name="idpAlias">The alias of an Identity Provider linked to the user</param>
    /// <param name="idpUserId">The userId at an Identity Provider linked to the user</param>
    /// <param name="lastName">A String contained in lastName, or the complete lastName, if param "exact" is true</param>
    /// <param name="max">Maximum results size (defaults to 100)</param>
    /// <param name="q">A query to search for custom attributes, in the format 'key1:value2 key2:value2'</param>
    /// <param name="search">A String contained in username, first or last name, or email. Default search behavior is prefix-based (e.g., foo or foo*). Use foo for infix search and "foo" for exact search.</param>
    /// <param name="username"></param>
    /// <param name="cancellationToken">A String contained in username, or the complete username, if param "exact" is true</param>
    /// <returns></returns>
    Task<IEnumerable<UserRepresentation>> GetUsersAsync(string realm, bool? briefRepresentation = null,
        string? email = null,
        bool? emailVerified = null,
        bool? enabled = null,
        bool? exact = null,
        int? first = null,
        string? firstName = null,
        string? idpAlias = null,
        string? idpUserId = null,
        string? lastName = null,
        int? max = null,
        string? q = null,
        string? search = null,
        string? username = null,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<UserRepresentation>> GetUsersAsync(bool? briefRepresentation = null,
        string? email = null,
        bool? emailVerified = null,
        bool? enabled = null,
        bool? exact = null,
        int? first = null,
        string? firstName = null,
        string? idpAlias = null,
        string? idpUserId = null,
        string? lastName = null,
        int? max = null,
        string? q = null,
        string? search = null,
        string? username = null,
        CancellationToken cancellationToken = default);

    Task<ResponseBase<UserRepresentation>> CreateUserAsync(string realm, UserRepresentation user, CancellationToken cancellationToken = default);
    Task<ResponseBase<UserRepresentation>> CreateUserAsync(UserRepresentation user, CancellationToken cancellationToken = default);

    Task<UserRepresentation?> GetUserAsync(string realm, string userId, CancellationToken cancellationToken = default);
    Task<UserRepresentation?> GetUserAsync(string userId, CancellationToken cancellationToken = default);

    Task<bool> ResetPasswordAsync(string realm, string userId, ResetPassword password, CancellationToken cancellationToken = default);
    Task<bool> ResetPasswordAsync(string userId, ResetPassword password, CancellationToken cancellationToken = default);

    Task<bool> LogoutAllUserSessions(string realm, string userId, CancellationToken cancellationToken = default);
    Task<bool> LogoutAllUserSessions(string userId, CancellationToken cancellationToken = default);

    Task<bool> UpdateUserAsync(string realm, string id, UserRepresentation user, CancellationToken cancellationToken = default);
    Task<bool> UpdateUserAsync(string id, UserRepresentation user, CancellationToken cancellationToken = default);

    Task<bool> DeleteUserAsync(string realm, string id, CancellationToken cancellationToken = default);
    Task<bool> DeleteUserAsync(string id, CancellationToken cancellationToken = default);
}
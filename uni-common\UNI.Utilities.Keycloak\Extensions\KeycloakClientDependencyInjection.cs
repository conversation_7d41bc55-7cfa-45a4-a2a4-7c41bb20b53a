using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using UNI.Utilities.Keycloak.Handlers;
using UNI.Utilities.Keycloak.Models;
using UNI.Utilities.Keycloak.Services;
using UNI.Utilities.Keycloak.Store;

namespace UNI.Utilities.Keycloak.Extensions
{
    public static class KeycloakClientDependencyInjection
    {
        public static IServiceCollection AddKeyCloakClient(this IServiceCollection services, Action<Settings> configure)
        {
            var settings = new Settings();
            configure(settings);

            services.TryAddSingleton(settings);


            services.AddHttpClient<IKeycloakAuthService, KeycloakAuthService>()
                .ConfigureHttpClient(c => c.BaseAddress = new Uri(settings.BaseUrl ?? throw new InvalidOperationException()));

            services.TryAddTransient<AuthHandler>();
            services.AddSingleton<ITokenStore, TokenStore>();


            services.AddHttpClient<IKeycloakClient, KeycloakClient>()
                .ConfigureHttpClient(c => c.BaseAddress = new Uri(settings.BaseUrl ?? throw new InvalidOperationException()))
                .AddHttpMessageHandler<AuthHandler>();


            return services;
        }

        public static IServiceCollection AddKeyCloakClient(this IServiceCollection services, IConfigurationSection configure)
        {
            return AddKeyCloakClient(services, c =>
            {
                c.BaseUrl = configure["BaseUrl"];
                c.Realm = configure["Realm"];
                c.Credentials = configure.GetSection("Credentials").Get<Credentials>();
            });
        }
        public static IServiceCollection AddKeyCloakClient(this IServiceCollection services, Settings configure)
        {
            return AddKeyCloakClient(services, c =>
            {
                c.BaseUrl = configure.BaseUrl;
                c.Realm = configure.Realm;
                c.Credentials = configure.Credentials;
            });
        }
        public static IServiceCollection AddKeyCloakAuthClient(this IServiceCollection services, Action<Settings> configure)
        {
            var settings = new Settings();
            configure(settings);

            services.TryAddSingleton(settings);

            services.AddHttpClient<IKeycloakAuthService, KeycloakAuthService>()
                .ConfigureHttpClient(c => c.BaseAddress = new Uri(settings.BaseUrl));

            services.TryAddTransient<IKeycloakAuthService>();
            return services;
        }
    }
}

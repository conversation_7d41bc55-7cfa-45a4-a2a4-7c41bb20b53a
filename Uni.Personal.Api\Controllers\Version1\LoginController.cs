using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using Uni.Personal.BLL.Interfaces;
using UNI.Common;
using UNI.Model;
using UNI.Model.Api;
using UNI.Model.APPM;
using UNI.Model.Core;
using UNI.Utils;

namespace Uni.Personal.Api.Controllers.Version1
{

    /// <summary>
    /// Super App
    /// </summary>
    /// Author: duongpx
    /// CreatedDate: 07/02/2020 9:31 AM
    /// <seealso cref="UniController" />
    [ApiController]
    [Route("api/v1/login/[action]")]
    public class LoginController : UniController
    {
        private const string _PREFIX = "";

        private readonly IAppUserService _userService;
        private readonly IAppManagerService _appService;
        //private readonly IKafkaNotificationService _kafkaProducerService;
        //private readonly IOtpService _otpService;
        //private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        //private readonly IAmazonS3 _s3Client;
        /// <summary>
        /// Initializes a new instance of the <see cref="LoginController"/> class.
        /// </summary>
        /// <param name="userService"></param>
        /// <param name="appService"></param>
        /// <param name="appSettings"></param>
        /// <param name="configuration"></param>
        /// <param name="logger"></param>
        /// <param name="httpClient"></param>
        public LoginController(
            IAppUserService userService,
            IAppManagerService appService,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger,
            IConfiguration configuration,
            HttpClient httpClient) : base(appSettings, logger)
        {
            _userService = userService;
            //_appService = appService;
            //_kafkaProducerService = kafkaProducerService;
            //_otpService = otpService;
            _configuration = configuration;
            _httpClient = httpClient;
            // Khởi tạo Amazon S3 client
            //_s3Client = new AmazonS3Client(
            //    _configuration["AWS:AccessKey"],
            //    _configuration["AWS:SecretKey"],
            //    RegionEndpoint.GetBySystemName(_configuration["AWS:Region"])
            //);

        }

        #region "User Login"        
        /// <summary>
        /// SetUserRegister - Đăng ký người dùng
        /// </summary>
        /// <param name="register">lấy mã secret_cd truyền kiểm tra </param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<BaseResponse<coreUserLoginResponse>> SetUserRegister([FromBody] coreUserLoginReg register)
        {
            if (!this.ModelState.IsValid)
            {
                return GetErrorResponse<coreUserLoginResponse>(ApiResult.Invalid, (int)ErrorCode.ModelInvalid, ErrorCode.ModelInvalid.GetDescription());
            }
            if (!register.phone.StartsWith("0"))
            {
                register.phone = "0" + register.phone;
            }
            if (!string.IsNullOrEmpty(register.email) && !Utils.isEmailValid(register.email))
            {
                return GetErrorResponse<coreUserLoginResponse>(ApiResult.Invalid, (int)ErrorCode.InvalidEmail,
                    ErrorCode.InvalidEmail.GetDescription());
            }
            if (register.verifyType == 0 && !Utils.IsPhoneNumberVN(register.phone))
            {
                return GetErrorResponse<coreUserLoginResponse>(ApiResult.Invalid, (int)ErrorCode.InvalidPhone,
                    ErrorCode.InvalidPhone.GetDescription());
            }
            register.loginName = register.phone;
            if (register.loginName.Any(ch => !Char.IsLetterOrDigit(ch) && ch != '_'))
            {
                return GetErrorResponse<coreUserLoginResponse>(ApiResult.Invalid, (int)ErrorCode.UsernameSpec,
                    ErrorCode.UsernameSpec.GetDescription());
            }
            var result = await _userService.SetUserRegister(register);
            if (result != null && result.valid)
            {
                //var otp = await _kafkaProducerService.SendSmsAsync("OTP", result.phone);
                var gToken = new WalUserGrant(result.userId, result.phone, result.email, result.verifyType);
                var otp = await _appService.TakeOTP(this.CtrlClient, gToken);
                if (otp == null)
                {
                    return GetErrorResponse<coreUserLoginResponse>(ApiResult.Invalid, 2, "Lỗi không tạo được OTP");
                }
                if (!otp.valid)
                {
                    return GetErrorResponse<coreUserLoginResponse>(ApiResult.Invalid, 2, otp.messages);
                }
                result.secret_cd = otp.secret_cd;
                return GetResponse(ApiResult.Success, result);
            }
            else
            {
                return GetErrorResponse<coreUserLoginResponse>(ApiResult.Invalid, 2, result.messages);
            }
        }
        /// <summary>
        /// SetVerifyCode - Xác minh OTP khi xác nhận tạo người dùng
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpPut]
        [AllowAnonymous]
        public async Task<BaseResponse<userLoginRefesh>> SetVerifyCode([FromBody] coreVerify code)
        {
            if (!this.ModelState.IsValid)
            {
                return GetErrorResponse<userLoginRefesh>(ApiResult.Invalid, (int)ErrorCode.ModelInvalid, ErrorCode.ModelInvalid.GetDescription());
            }
            var registed = await _userService.GetUserRegisted(code.reg_id);
            if (registed == null)
            {
                return GetErrorResponse<userLoginRefesh>(ApiResult.Invalid, 2, "Dữ liệu không hợp lệ");
            }
            var code1 = new userVerification { userId = code.reg_id, verificationCode = code.code, secret_cd = code.secret_cd, tokenType = registed.verifyType };
            var verify = await _appService.SetVerificationCode(this.UserId, code1);
            //var verify = await _otpService.ValidateOtpAsync(registed.loginName, code.code, Request.Host.ToString(), code.secret_cd);
            if (verify.Status == 1)
            {
                var login = await _userService.SetVerificated(this.CtrlClient, code, registed);
                if (login != null)
                {
                    return GetResponse(ApiResult.Success, login);
                }
                else
                {
                    return GetErrorResponse<userLoginRefesh>(ApiResult.Fail, (int)ErrorCode.CreateUserIdentity,
                        ErrorCode.CreateUserIdentity.GetDescription());
                }
            }
            else
            {
                //if (result.Blocked)
                //{
                //    await _userService.SetLockUser(registed.userId, true);
                //}
                return GetErrorResponse<userLoginRefesh>(ApiResult.Fail, 0, "OTP không hợp lệ");
            }
        }
        /// <summary>
        /// SetResendCode - Lấy lại mã OTP khi bị hết hạn hoặc chưa nhận 
        /// </summary>
        /// <returns></returns>
        /// 
        //
        [HttpPut]
        [AllowAnonymous]
        public async Task<BaseResponse<OtpMessageResponse>> SetResendCode([FromBody] userForegetSetRespone login)
        {
            if (!this.ModelState.IsValid)
            {
                return GetErrorResponse<OtpMessageResponse>(ApiResult.Invalid, (int)ErrorCode.ModelInvalid, ErrorCode.ModelInvalid.GetDescription());
            }
            var result = await _userService.SetResendCode(login.reg_id);
            if (result != null && result.valid)
            {
                var gToken = new WalUserGrant(result.userId, result.phone, result.email, result.verifyType);
                var otp = await _appService.TakeOTP(this.CtrlClient, gToken);
                //var otp = await _kafkaProducerService.SendSmsAsync("OTP", login.phone);
                if (otp == null)
                {
                    return GetErrorResponse<OtpMessageResponse>(ApiResult.Invalid, 2, "Lỗi không tạo được OTP");
                }
                if (!otp.valid)
                {
                    return GetErrorResponse<OtpMessageResponse>(ApiResult.Invalid, 2, otp.messages);
                }
                var res = new OtpMessageResponse
                {
                    valid = true,
                    secret_cd = otp.secret_cd,
                    messages = otp.messages,
                };
                return GetResponse<OtpMessageResponse>(ApiResult.Success, res);
            }
            else
            {
                return GetErrorResponse<OtpMessageResponse>(ApiResult.Fail, 2, result.messages);
            }
        }

        /// <summary>
        /// Set User Profile and Actived - Cập nhật thông tin hồ sơ cá nhân
        /// </summary>
        /// <param name="upgrade"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<BaseResponse<string>> SetUserProfileUpgrade([FromBody] UserProfileSet upgrade)
        {
            var rs = await _userService.SetUserProfile(UserId, upgrade);
            return GetResponse<string>(ApiResult.Success, rs.messages);
        }
        /// <summary>
        /// Set Verification Refresh
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        public async Task<BaseResponse<OtpMessageResponse>> SetVerificationRefresh([FromBody] userBasePhone login)
        {
            string userLogin = login.phone;
            if (login.loginName != null)
            {
                userLogin = login.loginName;
            }
            var result = await _userService.GetUserProfile(userLogin);
            if (result != null && result.isVerify == 0)
            {
                var otp = await _appService.TakeOTP(this.CtrlClient, new WalUserGrant(userLogin, login.phone, "", login.tokenType, "Unicloud"));
                if (otp == null)
                {
                    return GetErrorResponse<OtpMessageResponse>(ApiResult.Invalid, 2, "Lỗi không tạo được OTP");
                }
                if (!otp.valid)
                {
                    return GetErrorResponse<OtpMessageResponse>(ApiResult.Invalid, 2, otp.messages);
                }
                return GetResponse<OtpMessageResponse>(ApiResult.Success, otp.response());
            }
            else
            {
                return GetResponse<OtpMessageResponse>(ApiResult.Fail, null);
            }
        }

        #endregion login
        /// <summary>
        /// Reset Password - Cập nhật mật khẩu
        /// </summary>
        /// <param name="passwordSet"></param>
        /// <returns></returns>
        /// 
        [HttpPut]
        [Authorize]
        public async Task<BaseResponse<string>> SetUserPassword([FromBody] SetUserPassword passwordSet)
        {
            //kiem tra xem user trong token phải trùng với user truyền vào thì mới cho reset password
            var userName = User.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value;
            if (userName != passwordSet.UserLogin)
            {
                return GetResponse<string>(ApiResult.Fail, "Tên đăng nhập không đúng");
            }
            var result = await _userService.ResetPassword(passwordSet);
            if (result)
                return GetResponse<string>(ApiResult.Success, "Success");
            else
                return GetResponse<string>(ApiResult.Fail, "Fail");
        }

        /// <summary>
        /// SetUserForgetPassword - Gửi yê cầu xác minh
        /// </summary>
        /// <param name="forget"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<BaseResponse<userForegetSetRespone>> SetUserForgetPassword([FromBody] userForegetSet forget)
        {
            var result = await _userService.SetUserForgetPassword(this.CtrlClient, forget);
            if (result == null)
            {
                return GetErrorResponse<userForegetSetRespone>(ApiResult.Fail, 2, result.messages);
            }
            if (!result.valid)
            {
                return GetErrorResponse<userForegetSetRespone>(ApiResult.Fail, 2, result.messages);
            }
            if (result.userId == null)
            {
                return GetErrorResponse<userForegetSetRespone>(ApiResult.Invalid, 2, "Thông tin không hợp lệ");
            }
            var gToken = new WalUserGrant(result.userId, result.phone, result.email, result.verifyType);
            var otp = await _appService.TakeOTP(this.CtrlClient, gToken);
            //var otp = await _kafkaProducerService.SendSmsAsync("OTP", forget.loginName);
            if (otp == null)
            {
                return GetErrorResponse<userForegetSetRespone>(ApiResult.Invalid, 2, "Lỗi không tạo được OTP");
            }
            if (!otp.valid)
            {
                return GetErrorResponse<userForegetSetRespone>(ApiResult.Invalid, 2, otp.messages);
            }
            //if (!otp.valid)
            //{
            //    return GetErrorResponse<userForegetSetRespone>(ApiResult.Invalid, 2, otp.messages);
            //}
            var fget = await _userService.GetUserForgetPassword(this.CtrlClient, forget.loginName, forget.udid);
            return GetResponse<userForegetSetRespone>(ApiResult.Success,
                new userForegetSetRespone
                {
                    reg_id = result.reg_id,
                    secret_cd = otp.secret_cd,
                    phone = fget.phone,
                    email = fget.email,
                    verifyType = result.verifyType
                });

        }
        /// <summary>
        /// SetUserForgetVerifyCode - Xác minh để tạo lại mật khẩu
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>       
        [HttpPut]
        [AllowAnonymous]
        public async Task<BaseResponse<userLoginRefesh>> SetUserForgetVerifyCode([FromBody] coreVerify code)
        {
            if (!this.ModelState.IsValid)
            {
                return GetErrorResponse<userLoginRefesh>(ApiResult.Invalid, (int)ErrorCode.ModelInvalid, ErrorCode.ModelInvalid.GetDescription());
            }
            var registed = await _userService.GetUserRegisted(code.reg_id);
            if (registed == null)
            {
                return GetErrorResponse<userLoginRefesh>(ApiResult.Invalid, 2, "Dữ liệu không hợp lệ");
            }
            var code1 = new userVerification { userId = code.reg_id, verificationCode = code.code, secret_cd = code.secret_cd, tokenType = registed.verifyType };
            var verify = await _appService.SetVerificationCode(registed.userId, code1);
            //var verify = await _otpService.ValidateOtpAsync(registed.loginName, code.code, Request.Host.ToString(), code.secret_cd);
            if (verify.Status == 1)
            {
                var login = await _userService.SetUserForgetVerificated(code, registed, 1);
                return GetResponse(ApiResult.Success, login);
            }
            else
            {
                return GetErrorResponse<userLoginRefesh>(ApiResult.Fail, 0, "OTP không hợp lệ");
            }
        }

        [HttpPost]
        [AllowAnonymous]
        //[ApiKey]
        public async Task<BaseResponse<string>> SetMessage([FromBody] MessageSend message)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return GetErrorResponse<string>(ApiResult.Invalid, (int)ErrorCode.ModelInvalid, ErrorCode.ModelInvalid.GetDescription());
                }
                var result = await _appService.TakeMessage(CtrlClient, message);
                return GetResponse(ApiResult.Success, "Pass");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.StackTrace);
                return GetResponse(ApiResult.Success, "fall");
            }
        }

        #region profile app
        /// <summary>
        /// Get Profile By Id - Lấy thôn tin hồ sơ cá nhân
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<UserProfileFull>> GetProfileById()
        {
            var result = await _userService.GetProfileFull(null);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// Get Profile Fields - Lấy danh sách field của hồ sơ
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<viewField>>> GetProfileFields()
        {
            var result = await _userService.GetProfileFields(this.UserId, null);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// Set Profile field - Cập nhật các field hồ sơ
        /// </summary>
        /// <param name="field"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<BaseResponse<string>> SetProfileFields([FromBody] viewField field)
        {
            await _userService.SetProfileFields(this.UserId, field);
            return GetResponse<string>(ApiResult.Success, null);
        }
        
        #endregion profile app
        /// <summary>
        /// Login
        /// </summary>
        /// <param name="loginRequest"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<BaseResponse<LoginResponse>> Login([FromBody] LoginRequest loginRequest)
        {
            var tokenEndpoint = $"{_configuration["Jwt:ClientUrl"]}/realms/{_configuration["Jwt:ClientRealm"]}/protocol/openid-connect/token";
            var clientId = _configuration["Jwt:ClientId"];
            var clientSecret = _configuration["Jwt:ClientSecret"];

            var content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("client_id", clientId),
                new KeyValuePair<string, string>("client_secret", clientSecret),
                new KeyValuePair<string, string>("grant_type", "password"),
                new KeyValuePair<string, string>("username", loginRequest.Username),
                new KeyValuePair<string, string>("password", loginRequest.Password),
                new KeyValuePair<string, string>("scope", "openid profile email")
            });

            var response = await _httpClient.PostAsync(tokenEndpoint, content);
            if (!response.IsSuccessStatusCode)
            {
                return GetErrorResponse<LoginResponse>(ApiResult.Fail, 2, "Số điện thoại hoặc mật khẩu không đúng");
            }

            var responseString = await response.Content.ReadAsStringAsync();
            var tokenResponse = JObject.Parse(responseString);
            var loginResponse = new LoginResponse();
            loginResponse.token = tokenResponse["access_token"].ToString();
            loginResponse.refreshToken = tokenResponse["refresh_token"].ToString();

            //test push
            //_ = _kafkaProducerService.SendPushAsync("PUSHTEST", "0982180976", new Dictionary<string, string> { });


            //gửi dữ liệu sang kafka
            //var result = _userService.GetUserProfile(loginRequest.Username);
            //if (result != null )
            //{
            //    // Send data to Kafka on successful login
            //    var userInfo = new
            //    {
            //        userName = loginRequest.Username,
            //        playerId = loginRequest.PlayerId,
            //        userId = result.userId,
            //        fullName = loginRequest.Username,
            //        email = result.email,
            //        phone = loginRequest.Username
            //    };
            //    _ = _kafkaProducerService.SendUserInfoAsync(userInfo);
            //}
                
            
            return GetResponse(ApiResult.Success, loginResponse);
        }
        /// <summary>
        /// LoginWithToken
        /// </summary>
        /// <param name="tokenRequest"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<BaseResponse<LoginResponse>> LoginWithToken([FromBody] TokenRequest tokenRequest)
        {
            var tokenEndpoint = $"{_configuration["Jwt:ClientUrl"]}/realms/{_configuration["Jwt:ClientRealm"]}/protocol/openid-connect/token";
            var clientId = _configuration["Jwt:ClientId"];
            var clientSecret = _configuration["Jwt:ClientSecret"];

            var content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("client_id", clientId),
                new KeyValuePair<string, string>("client_secret", clientSecret),
                new KeyValuePair<string, string>("grant_type", "refresh_token"),
                new KeyValuePair<string, string>("refresh_token", tokenRequest.token),
                new KeyValuePair<string, string>("scope", "openid profile email")
            });

            var response = await _httpClient.PostAsync(tokenEndpoint, content);
            if (!response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return GetResponse(ApiResult.Fail, new LoginResponse(), $"Refresh token failed: {responseContent}");
            }

            var responseString = await response.Content.ReadAsStringAsync();
            var tokenResponse = JObject.Parse(responseString);

            var loginResponse = new LoginResponse();
            loginResponse.token = tokenResponse["access_token"].ToString();
            loginResponse.refreshToken = tokenResponse["refresh_token"].ToString();
            return GetResponse(ApiResult.Success, loginResponse);
        }
        /// <summary>
        /// LoginWithToken
        /// </summary>
        /// <param name="tokenRequest"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<BaseResponse<LoginResponse>> LoginTokenAsync([FromBody] TokenRequest tokenRequest)
        {
            var tokenEndpoint = $"{_configuration["Jwt:ClientUrl"]}/realms/{_configuration["Jwt:ClientRealm"]}/protocol/openid-connect/token";
            var clientId = _configuration["Jwt:ClientId"];
            var clientSecret = _configuration["Jwt:ClientSecret"];
            var decodeToken = SecurityHelper.DecodeFrom64(tokenRequest.token);
            if (!SecurityHelper.EncodeTo64Utf8(clientId +":"+ clientSecret).Equals(tokenRequest.token))
            {
                return GetResponse(ApiResult.Fail, new LoginResponse(), $"Login token failed {SecurityHelper.EncodeTo64Utf8(clientId + clientSecret)}");
            }    
            var content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("client_id", clientId),
                new KeyValuePair<string, string>("client_secret", clientSecret),
                new KeyValuePair<string, string>("grant_type", "client_credentials")
            });

            var response = await _httpClient.PostAsync(tokenEndpoint, content);
            if (!response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return GetResponse(ApiResult.Fail, new LoginResponse(), $"Refresh token failed");
            }

            var responseString = await response.Content.ReadAsStringAsync();
            var tokenResponse = JObject.Parse(responseString);
            //return GetResponse(ApiResult.Fail, new LoginResponse(), $"token failed: {responseString}");

            var loginResponse = new LoginResponse();
            loginResponse.token = tokenResponse["access_token"].ToString();
            //loginResponse.refreshToken = tokenResponse["refresh_token"].ToString();
            return GetResponse(ApiResult.Success, loginResponse);
        }
        /// <summary>
        /// SetChangePassword - Đổi mật khẩu
        /// </summary>
        /// <param name="passwordSet"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> SetChangePassword([FromBody] UserPassWordChange passwordSet)
        {
            passwordSet.userId = this.UserId;
            var result = await _userService.SetChangePassword(passwordSet);
            if (result)
                return GetResponse<string>(ApiResult.Success, "Success");
            else
                return GetResponse<string>(ApiResult.Fail, "Fail");
        }
        [HttpGet]
        [AllowAnonymous]
        public async Task<BaseResponse<int>> HealthCheck()
        {
            return GetResponse(ApiResult.Success, 200);
        }


        #region device
        ///// <summary>
        ///// SetSmartDevice - Lưu thông tin thiết bị
        ///// </summary>
        ///// <param name="device"></param>
        ///// <returns></returns>
        ///// 
        //[HttpPost]
        //public async Task<BaseResponse<string>> SetSmartDevice([FromBody] userSmartDevice device)
        //{
        //    var result = await _userService.SetSmartDevice(this.CtrlClient, device);
        //    if (result.valid)
        //        return GetResponse<string>(ApiResult.Success, result.messages);
        //    else
        //        return GetErrorResponse<string>(ApiResult.Error, 3, result.messages);
        //}
        ///// <summary>
        ///// SetSmartDeviceConfirm
        ///// </summary>
        ///// <param name="confirm"></param>
        ///// <returns></returns>
        //[HttpPut]
        //public async Task<BaseResponse<userForegetSetRespone>> SetSmartDeviceConfirm([FromBody] userSmartDeviceConfirm confirm)
        //{
        //    var result = await _userService.SetSmartDeviceConfirm(this.CtrlClient, confirm);
        //    if (result.valid)
        //    {
        //        var gToken = new WalUserGrant(result.userId, result.phone, result.email, result.verifyType);
        //        var otp = await _appService.TakeOTP(this.CtrlClient, gToken);
        //        if (otp == null)
        //        {
        //            return GetErrorResponse<userForegetSetRespone>(ApiResult.Invalid, 2, "Lỗi không tạo được OTP");
        //        }
        //        if (!otp.valid)
        //        {
        //            return GetErrorResponse<userForegetSetRespone>(ApiResult.Invalid, 2, otp.messages);
        //        }
        //        return GetResponse<userForegetSetRespone>(ApiResult.Success,
        //            new userForegetSetRespone
        //            {
        //                reg_id = confirm.udid,
        //                secret_cd = otp.secret_cd,
        //                phone = result.phone,
        //                email = result.email,
        //                verifyType = result.verifyType
        //            });
        //    }
        //    else
        //    {
        //        return GetErrorResponse<userForegetSetRespone>(ApiResult.Error, 2, result.messages);
        //    }
        //}


        ///// <summary>
        ///// SetSmartDeviceVerify
        ///// </summary>
        ///// <param name="verify"></param>
        ///// <returns></returns>
        //[HttpPut]
        //public async Task<BaseResponse<string>> SetSmartDeviceVerify([FromBody] userSmartDeviceVerify verify)
        //{
        //    var smart = await _userService.GetSmartDeviceVerify(this.CtrlClient, verify);
        //    if (!smart.valid)
        //    {
        //        return GetErrorResponse<string>(ApiResult.Error, 2, smart.messages);
        //    }
        //    var code = new userVerification { userId = smart.userId, verificationCode = verify.otp, tokenType = smart.verifyType, secret_cd = verify.udid };
        //    var result = await _appService.SetVerificationCode(this.UserId, code);
        //    if (result.Status == 1)
        //    {
        //        var verified = await _userService.SetSmartDeviceVerificated(this.CtrlClient, verify, result.Status);
        //        if (verified.valid)
        //        {
        //            return GetResponse(ApiResult.Success, verified.messages);
        //        }
        //        else
        //        {
        //            return GetErrorResponse<string>(ApiResult.Fail, 2, result.StatusMessage);
        //        }
        //    }
        //    else
        //    {
        //        //if (result.Blocked)
        //        //{
        //        //    await _userService.SetLockUser(smart.userId, true);
        //        //}
        //        return GetErrorResponse<string>(ApiResult.Fail, result.Status, result.StatusMessage);
        //    }

        //}

        ///// <summary>
        ///// GetSmartDevices
        ///// </summary>
        ///// <param name="offSet"></param>
        ///// <param name="pageSize"></param>
        ///// <returns></returns>
        ///// 
        //[HttpGet]
        //public async Task<BaseResponse<userSmartDevicePage>> GetSmartDevices([FromQuery] int? offSet, [FromQuery] int? pageSize)
        //{
        //    var flt = new FilterBase(this.ClientId, this.UserId, offSet, pageSize);
        //    var result = await _userService.GetSmartDevices(flt);
        //    return GetResponse<userSmartDevicePage>(ApiResult.Success, result);
        //}
        ///// <summary>
        ///// DeleteSmartDevice
        ///// </summary>
        ///// <param name="udid"></param>
        ///// <returns></returns>
        ///// 
        //[HttpDelete]
        //public async Task<BaseResponse<string>> DeleteSmartDevice([FromQuery] string udid)
        //{
        //    var result = await _userService.DeleteSmartDevice(this.CtrlClient, udid);
        //    if (result.valid)
        //        return GetResponse<string>(ApiResult.Success, result.messages);
        //    else
        //        return GetErrorResponse<string>(ApiResult.Error, 2, result.messages);
        //}

        #endregion


    }
}

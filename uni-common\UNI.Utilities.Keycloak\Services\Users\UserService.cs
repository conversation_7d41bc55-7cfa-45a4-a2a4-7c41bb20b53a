using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using UNI.Utilities.HttpClientExtension;
using UNI.Utilities.Keycloak.Models;
using UNI.Utilities.Keycloak.Models.Users;
using UNI.Utilities.Keycloak.Services;

namespace UNI.Utilities.Keycloak.Services.Users
{

    public class UserService : ServiceBase, IUserService
    {
        public UserService(HttpClient client) : base(client)
        {
        }

        /// <summary>
        /// ctor with default realm
        /// </summary>
        /// <param name="client"></param>
        /// <param name="defaultRealm"></param>
        public UserService(HttpClient client, string defaultRealm) : base(client, defaultRealm)
        {
        }

        public async Task<IEnumerable<UserRepresentation>> GetUsersAsync(string realm, bool? briefRepresentation = null, string? email = null,
       bool? emailVerified = null,
       bool? enabled = null, bool? exact = null, int? first = null, string? firstName = null,
       string? idpAlias = null,
       string? idpUserId = null, string? lastName = null, int? max = null, string? q = null, string? search = null,
       string? username = null, CancellationToken cancellationToken = default)
        {
            var query = new
            {
                briefRepresentation,
                email,
                emailVerified,
                enabled,
                exact,
                first,
                firstName,
                idpAlias,
                idpUserId,
                lastName,
                max,
                q,
                search,
                username
            };

            var rs = await HttpClient.GetAsync<IEnumerable<UserRepresentation>>(Endpoints.UserEndpoints.Users(realm), query, cancellationToken: cancellationToken);
            return rs.Content ?? Array.Empty<UserRepresentation>();
        }

        public async Task<ResponseBase<UserRepresentation>> CreateUserAsync(string realm, UserRepresentation user, CancellationToken cancellationToken = default)
        {
            var rs = await HttpClient.PostJsonAsync<bool>(Endpoints.UserEndpoints.Users(realm), user, cancellationToken);
            var rp = new ResponseBase<UserRepresentation>
            {
                IsSuccessStatusCode = rs.IsSuccessStatusCode,
                StatusCode = rs.StatusCode,
                StatusDescription = rs.StatusDescription
            };
            if (!rs.IsSuccessStatusCode || rs.HttpResponseHeaders == null) return rp;

            var userId = rs.HttpResponseHeaders.Location.ToString().Split("/")[^1];
            user.Id = userId;
            rp.Data = user;

            return rp;
        }

        public async Task<UserRepresentation?> GetUserAsync(string realm, string userId, CancellationToken cancellationToken = default)
        {
            var rs = await HttpClient.GetAsync<UserRepresentation>(Endpoints.UserEndpoints.User(realm, userId), cancellationToken: cancellationToken);
            return rs.Content;
        }
        public async Task<bool> ResetPasswordAsync(string realm, string userId, ResetPassword password,
            CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.PutJsonAsync<string>(Endpoints.UserEndpoints.ResetPassword(realm, userId), password, cancellationToken: cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> LogoutAllUserSessions(string realm, string userId, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.PostAsync(Endpoints.UserEndpoints.RemoveAllUserSessions(realm, userId), null, cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> UpdateUserAsync(string realm, string id, UserRepresentation user, CancellationToken cancellationToken = default)
        {
            var rs = await HttpClient.PutJsonAsync<bool>(Endpoints.UserEndpoints.User(realm, id), user, cancellationToken: cancellationToken);
            return rs.IsSuccessStatusCode;
        }

        public async Task<bool> DeleteUserAsync(string realm, string id, CancellationToken cancellationToken = default)
        {
            var rs = await HttpClient.DeleteAsync(Endpoints.UserEndpoints.User(realm, id), cancellationToken: cancellationToken);
            return rs.IsSuccessStatusCode;
        }

        // Default realm overloads
        public async Task<IEnumerable<UserRepresentation>> GetUsersAsync(bool? briefRepresentation = null, string? email = null,
            bool? emailVerified = null, bool? enabled = null, bool? exact = null, int? first = null, string? firstName = null,
            string? idpAlias = null, string? idpUserId = null, string? lastName = null, int? max = null, string? q = null,
            string? search = null, string? username = null, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(DefaultRealm))
                throw new InvalidOperationException("Default realm is not configured. Please provide a realm parameter or configure a default realm.");

            return await GetUsersAsync(DefaultRealm, briefRepresentation, email, emailVerified, enabled, exact, first,
                firstName, idpAlias, idpUserId, lastName, max, q, search, username, cancellationToken);
        }

        public async Task<ResponseBase<UserRepresentation>> CreateUserAsync(UserRepresentation user, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(DefaultRealm))
                throw new InvalidOperationException("Default realm is not configured. Please provide a realm parameter or configure a default realm.");

            return await CreateUserAsync(DefaultRealm, user, cancellationToken);
        }

        public async Task<UserRepresentation?> GetUserAsync(string userId, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(DefaultRealm))
                throw new InvalidOperationException("Default realm is not configured. Please provide a realm parameter or configure a default realm.");

            return await GetUserAsync(DefaultRealm, userId, cancellationToken);
        }

        public async Task<bool> ResetPasswordAsync(string userId, ResetPassword password, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(DefaultRealm))
                throw new InvalidOperationException("Default realm is not configured. Please provide a realm parameter or configure a default realm.");

            return await ResetPasswordAsync(DefaultRealm, userId, password, cancellationToken);
        }

        public async Task<bool> LogoutAllUserSessions(string userId, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(DefaultRealm))
                throw new InvalidOperationException("Default realm is not configured. Please provide a realm parameter or configure a default realm.");

            return await LogoutAllUserSessions(DefaultRealm, userId, cancellationToken);
        }

        public async Task<bool> UpdateUserAsync(string id, UserRepresentation user, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(DefaultRealm))
                throw new InvalidOperationException("Default realm is not configured. Please provide a realm parameter or configure a default realm.");

            return await UpdateUserAsync(DefaultRealm, id, user, cancellationToken);
        }

        public async Task<bool> DeleteUserAsync(string id, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(DefaultRealm))
                throw new InvalidOperationException("Default realm is not configured. Please provide a realm parameter or configure a default realm.");

            return await DeleteUserAsync(DefaultRealm, id, cancellationToken);
        }
    }
}
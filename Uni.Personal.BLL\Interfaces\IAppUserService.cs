﻿using Keycloak.Net.Models.Users;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Model;
using UNI.Model.Account;
using UNI.Model.APPM;
using UNI.Model.Core;
using UNI.Utilities.Keycloak.Models.Users;

namespace Uni.Personal.BLL.Interfaces
{
    /// <summary>
    /// Interface IUserService
    /// <author>Tai NT</author>
    /// <date>2015/12/02</date>
    /// </summary>
    public interface IAppUserService
    {
        Task<coreUserLoginResponse> SetUserRegister(coreUserLoginReg reg);
        Task<coreUserLoginResponse> GetUserRegisted(string reg_id);
        Task<userLoginRefesh?> SetVerificated(BaseCtrlClient clt, coreVerify code, coreUserLoginResponse registed);
        Task<bool> SetLockUser(string userId, bool isLock);
        Task<bool> ResetPassword(SetUserPassword passwordSet, string? userId = null);
        // Task<User> FindUserByName(string userName);
        Task<UserRepresentation?> SetCreateAuthenUser(RegisterUserModel user);
        Task<userProfileSet> GetUserProfile(string loginUser);
        Task<BaseValidate> SetUserProfile(string userId, UserProfileSet ureg);
        Task<userForegetResponse> GetUserForgetPassword(BaseCtrlClient clt, string loginName, string udid);
        Task<coreUserLoginResponse> SetUserForgetPassword(BaseCtrlClient clt, userForegetSet forget);
        Task<userLoginRefesh?> SetUserForgetVerificated(coreVerify code, coreUserLoginResponse registed, int user_type);
        Task<coreUserLoginResponse> SetResendCode(string reg_id);
        Task<bool> SetChangePassword(UserPassWordChange passwordSet);
        Task<UserProfileFull> GetProfileFull(string loginName);
        Task<List<viewField>> GetProfileFields(string userId, string loginName);
        Task SetProfileFields(string userId, viewField fields);

        #region device
        // Task<BaseValidate> SetSmartDevice(BaseCtrlClient clt, userSmartDevice device);
        // Task<BaseValidate> DeleteSmartDevice(BaseCtrlClient clt, string udid);
        // Task<userSmartDevicePage> GetSmartDevices(FilterBase flt);
        // Task<userOtpResponse> SetSmartDeviceConfirm(BaseCtrlClient clt, userSmartDeviceConfirm confirm);
        // Task<userOtpResponse> GetSmartDeviceVerify(BaseCtrlClient clt, userSmartDeviceVerify verify);
        // Task<BaseValidate> SetSmartDeviceVerificated(BaseCtrlClient clt, userSmartDeviceVerify verify, int status);

        #endregion device
    }
}

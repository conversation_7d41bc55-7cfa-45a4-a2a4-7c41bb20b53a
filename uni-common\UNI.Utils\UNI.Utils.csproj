﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>UNI.Utils</AssemblyName>
    <PackageId>UNI.Utils</PackageId>   
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <LangVersion>latest</LangVersion> 
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="CyberSignature\**" />
    <Compile Remove="Libs\**" />
    <Compile Remove="SepeinSignature\**" />
    <EmbeddedResource Remove="CyberSignature\**" />
    <EmbeddedResource Remove="Libs\**" />
    <EmbeddedResource Remove="SepeinSignature\**" />
    <None Remove="CyberSignature\**" />
    <None Remove="Libs\**" />
    <None Remove="SepeinSignature\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="CyberSignatureHelpers.cs" />
    <Compile Remove="HanetUtils.cs" />
    <Compile Remove="ImageHelper.cs" />
    <Compile Remove="SepeinSignatureHelpers.cs" />
    <Compile Remove="VietQr\LibraryResources.Designer.cs" />
    <Compile Remove="WordUtils.cs" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="VietQr\LibraryResources.es.resx" />
    <EmbeddedResource Remove="VietQr\LibraryResources.resx" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Libs\Aspose.Words.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Include="Libs\Aspose.License.Lic" />
    <None Include="Libs\Aspose.Words.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Libs\Aspose.Words.Pdf2Word.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <!-- <PackageReference Include="Gma.QrCodeNet.Encoding" Version="1.0.1" /> -->
    <PackageReference Include="Aspose.Cells" Version="24.1.0" />
    <PackageReference Include="Aspose.PDF" Version="24.1.0" />
    <PackageReference Include="FreeSpire.Doc" Version="12.2.0" />
    <PackageReference Include="Google.Apis.Docs.v1" Version="1.51.0.2252" />
    <PackageReference Include="Google.Apis.Drive.v3" Version="1.51.0.2265" />
    <PackageReference Include="Google.Cloud.Storage.V1" Version="3.4.0" />
    <PackageReference Include="Markdig" Version="0.37.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.ViewFeatures" Version="2.1.3" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.11.0" />
    <PackageReference Include="Microsoft.NETCore.Runtime" Version="1.0.1" />
    <PackageReference Include="MiniWord" Version="0.6.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="QRCoder" Version="1.4.1" />
    <PackageReference Include="ReverseMarkdown" Version="4.4.0" />
    <PackageReference Include="runtime.native.System.Security.Cryptography" Version="4.3.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="SimpleHelpers.MemoryCache" Version="1.1.1" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.80.3" />
    <PackageReference Include="System.ComponentModel.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageReference Include="System.Formats.Asn1" Version="9.0.2" />
    <PackageReference Include="System.Linq" Version="4.3.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Reflection.TypeExtensions" Version="4.7.0" />
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageReference Include="System.Security.Cryptography.Encoding" Version="4.3.0" />
    <PackageReference Include="System.Security.Cryptography.Xml" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.2" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageReference Include="System.Xml.ReaderWriter" Version="4.3.0" />
    <PackageReference Include="System.Xml.XmlDocument" Version="4.3.0" />
	  <PackageReference Include="TMS.FlexCel" Version="7.6.4" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Aspose.Words">
      <HintPath>Libs\Aspose.Words.dll</HintPath>
    </Reference>
    <Reference Include="Aspose.Words.Pdf2Word">
      <HintPath>Libs\Aspose.Words.Pdf2Word.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Update="Libs\Aspose.License.Lic">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>

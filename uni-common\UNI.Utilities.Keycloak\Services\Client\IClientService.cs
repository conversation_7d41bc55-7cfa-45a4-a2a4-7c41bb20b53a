﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using UNI.Utilities.Keycloak.Models;

namespace UNI.Utilities.Keycloak.Services.Client;

public interface IClientService
{
    Task<bool?> CreateClientAsync(string realm, ClientRepresentationDto client, CancellationToken cancellationToken = default);
    Task<bool?> CreateClientAsync(ClientRepresentationDto client, CancellationToken cancellationToken = default);

    Task<ClientRepresentation?> GetClientAsync(string realm, string clientId, CancellationToken cancellationToken = default);
    Task<ClientRepresentation?> GetClientAsync(string clientId, CancellationToken cancellationToken = default);

    Task<IEnumerable<ClientRepresentation>?> GetClientsAsync(
        string realm,
        string? clientId = null,
        bool? viewableOnly = null,
        int? first = null,
        int? max = null,
        string? search = null, CancellationToken cancellationToken = default);

    Task<IEnumerable<ClientRepresentation>?> GetClientsAsync(
        string? clientId = null,
        bool? viewableOnly = null,
        int? first = null,
        int? max = null,
        string? search = null, CancellationToken cancellationToken = default);

    Task DeleteClientAsync(string realm, string clientId, CancellationToken cancellationToken = default);
    Task DeleteClientAsync(string clientId, CancellationToken cancellationToken = default);
}
﻿using Aspose.Pdf.Operators;
using Google.Apis.Auth.OAuth2;
using Google.Cloud.Storage.V1;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Object = Google.Apis.Storage.v1.Data.Object;

namespace UNI.Common.HelperService
{
    public class FireBaseServices
    {
        public static async Task<Object> UploadFile(Stream fileStream, string filename = null, string bucket = "sunshine-app-production.appspot.com", string app = "scrm", string format = "application/pdf")
        {
            try
            {
                if (fileStream == null || string.IsNullOrEmpty(filename))
                {
                    throw new Exception("Error: fileStream null or filename null");
                }
                
                var credentialPath = Environment.GetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS");
                Console.WriteLine("credentialPath: " + credentialPath.ToString());
                var credential = GoogleCredential.FromFile(credentialPath);

                var storageClient = await StorageClient.CreateAsync(credential);
                var appPath = $"{app}/{filename ?? Guid.NewGuid().ToString()}";
                var rs = await storageClient.UploadObjectAsync(bucket, appPath, format, fileStream);

                Console.WriteLine("Upload file done: " + appPath);

                return rs;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Lỗi upload file: " + ex);
                throw;
            }
        }

        public static async Task<string> UploadFileCdn(Stream fileStream, string filename = null, string bucket = "sunshine-app-production.appspot.com", string app = "scrm")
        {
            try
            {
                if (fileStream == null || string.IsNullOrEmpty(filename))
                {
                    throw new Exception("Error: fileStream null or filename null");
                }
                string format = "application/pdf";
                if (!string.IsNullOrEmpty(format))
                {
                    format = "application/" + Path.GetExtension(filename).Substring(1);
                }    

                var credentialPath = Environment.GetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS");                
                var credential = GoogleCredential.FromFile(credentialPath);

                var storageClient = await StorageClient.CreateAsync(credential);
                var appPath = $"{app}/{filename ?? Guid.NewGuid().ToString()}";
                var rs = await storageClient.UploadObjectAsync(bucket, appPath, format, fileStream);
                var linkUrl = rs.MediaLink.Replace("https://storage.googleapis.com/download/storage/v1/b/sunshine-app-production.appspot.com/o/", "https://cdn.sunshineapp.vn/");
                Console.WriteLine("Upload file done: " + appPath);
                return linkUrl;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Lỗi upload file cdn: " + ex);
                throw;
            }
        }
        private static string CoercePath(string path)
        {
            string resultstring = "";
            // although Google Cloud Storage objects exist in a flat namespace, using forward slashes allows the objects to
            // be exposed as nested subdirectories, e.g., when browsing via Google Cloud Console
            var lenthstr = path.IndexOf("?");
            if (lenthstr > 0)
            {
                path = path.Substring(0, lenthstr);
            }
            resultstring = path.Replace('\\', '/');
            resultstring = resultstring.Replace("%2F", "/");
            resultstring = resultstring.Replace("https://cdn.sunshineapp.vn/", "");
            return resultstring;
        }

        public static async Task<MemoryStream> GetAsync(string path, string bucket = "sunshine-app-production.appspot.com", CancellationToken cancellationToken = default)
        {

            var credentialPath = Environment.GetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS");
            // Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", credentialPath);
            var credential = GoogleCredential.FromFile(credentialPath);
            var stream = new MemoryStream();
            try
            {
                using (var storage = await StorageClient.CreateAsync(credential))
                {
                    await storage.DownloadObjectAsync(bucket, CoercePath(path), stream, cancellationToken: cancellationToken);
                    stream.Position = 0;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error: " + ex);
                throw;
            }
            return stream;
        }
    }
}
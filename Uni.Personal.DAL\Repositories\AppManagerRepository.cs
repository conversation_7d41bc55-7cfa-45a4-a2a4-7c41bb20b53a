using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RestSharp;
using System.Net.Http.Json;
using Uni.Personal.DAL.Interfaces;
using UNI.Common.Extensions;
using UNI.Model;
using UNI.Model.APPM;
using UNI.Utilities.HttpClientExtension;

namespace Uni.Personal.DAL.Repositories
{
    /// <summary>
    /// AppManagerRepository
    /// </summary>
    /// Author: 
    /// CreatedDate: 16/11/2016 2:07 PM
    /// <seealso cref="AppManagerRepository" />
    public class AppManagerRepository : IAppManagerRepository
    {
        private readonly ILogger<IAppManagerRepository> _logger;
        private readonly HttpClient _client;
        private readonly string _coreBaseUrl;
        private readonly string _coreApiKey;
        /// <summary>
        /// AppManager - 
        /// </summary>
        /// <param name="client"></param>
        /// <param name="logger"></param>
        /// <param name="configuration"></param>
        public AppManagerRepository(
            HttpClient client,
            ILogger<IAppManagerRepository> logger,
            IConfiguration configuration)
        {
            _client = client;
            _logger = logger;
            this._coreBaseUrl = configuration["Clients:ApiCore:BaseUrl"];
            this._coreApiKey = configuration["Clients:ApiCore:ApiKey"];
        }

        #region token-reg

        public async Task<OtpMessageGet> TakeOTP(BaseCtrlClient clt, WalUserGrant registed)
        {
            try
            {
                //var request = new RestRequest($"{_coreBaseUrl}/api/v1/coreTokenOTP/TakeTokenOTP1");
                //request.AddHeader("x-api-key", _coreApiKey);
                //request.AddJsonBody(registed);
                // var result = await _client.PostApiAsync<BaseResponse<OtpMessageGet>>(request);
                _client.DefaultRequestHeaders.Add("x-api-key", _coreApiKey);
                var result = await _client.PostJsonAsync<BaseResponse<OtpMessageGet>>($"{_coreBaseUrl}/api/v1/coreTokenOTP/TakeTokenOTP1", registed);
                return result.Content?.Data!;
            }
            catch (Exception ex)
            {
                _logger.LogError($"{ex}");
                throw;
            }
        }
        public async Task<ResponseCode> SetVerificationCode(string userId, userVerification code)
        {
            try
            {
                //var request = new RestRequest($"{_coreBaseUrl}/api/v1/coreTokenOTP/VerifyTokenOTP1");
                //request.AddHeader("x-api-key", _coreApiKey);
                //request.AddJsonBody(code);
                //var result = await _client.PostApiAsync<BaseResponse<ResponseCode>>(request);
                _client.DefaultRequestHeaders.Add("x-api-key", _coreApiKey);
                var result = await _client.PostJsonAsync<BaseResponse<ResponseCode>>($"{_coreBaseUrl}/api/v1/coreTokenOTP/VerifyTokenOTP1", code);
                return result.Content?.Data!;
            }
            catch (Exception ex)
            {
                _logger.LogError($"{ex}");
                throw;
            }
        }
        public async Task<BaseValidate> SetOtpStatus(string userId, userOtpStatus status)
        {
            try
            {
                //var request = new RestRequest($"{_coreBaseUrl}/api/v1/coreTokenOTP/SetOtpStatus1");
                //request.AddHeader("x-api-key", _coreApiKey);
                //request.AddQueryParameter("userId", userId);
                //request.AddJsonBody(status);
                _client.DefaultRequestHeaders.Add("x-api-key", _coreApiKey);
                var result = await _client.PostJsonAsync<BaseResponse<BaseValidate>>($"{_coreBaseUrl}/api/v1/coreTokenOTP/SetOtpStatus1?userId={userId}", status);
                return result.Content?.Data!;
            }
            catch (Exception ex)
            {
                _logger.LogError($"{ex}");
                throw ex;
            }
        }

        #endregion token-reg

        #region common-reg
        //public async Task<int> InsetSmartDeviceProfile(SmartDeviceSet deviceprofile)
        //{
        //    const string storedProcedure = "sp_SmartDevice_Set";
        //    try
        //    {
        //        using (SqlConnection connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@DeviceCd", deviceprofile.DeviceCd);
        //            param.Add("@DeviceType", deviceprofile.DeviceType);
        //            param.Add("@UserId", deviceprofile.UserId);
        //            param.Add("@GuestId", deviceprofile.GuestId);
        //            param.Add("@AppId", deviceprofile.aId);
        //            param.Add("@playerId", deviceprofile.playerId);
        //            var result = await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //            return result;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}

        #endregion common-reg


        //#region thread-reg
        //public ResponseList<List<fbThreadList>> GetThreadListByUser(FilterBase filter)
        //{
        //    const string storedProcedure = "sp_Thread_Info_ByUserId";
        //    try
        //    {
        //        using (SqlConnection connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@UserId", filter.userId);
        //            param.Add("@filter", filter.filter);
        //            param.Add("@Offset", filter.offSet);
        //            param.Add("@PageSize", filter.pageSize);
        //            param.Add("@Total", 0, DbType.Int64, ParameterDirection.InputOutput);
        //            param.Add("@TotalFiltered", 0, DbType.Int64, ParameterDirection.InputOutput);
        //            //param.Add("@TotalUnread", 0, DbType.Int64, ParameterDirection.InputOutput);

        //            var result = connection.Query<fbThreadList>(storedProcedure, param, commandType: CommandType.StoredProcedure).ToList();
        //            return new ResponseList<List<fbThreadList>>(result, param.Get<long>("@Total"), param.Get<long>("@TotalFiltered"));
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public async Task<fbThread> SetThreadFetch(string userId, fbThreadSet thread)
        //{
        //    const string storedProcedure = "sp_Thread_Info_Fetch";
        //    try
        //    {
        //        using (SqlConnection connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@UserId", userId);
        //            param.Add("@sub_prod_cd", thread.sub_prod_cd);
        //            param.Add("@project_cd", thread.project_cd);
        //            param.Add("@role_cd", thread.role_cd);
        //            param.Add("@cust_userid", thread.cust_userid);
        //            param.Add("@room_code", thread.room_code);
        //            var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //            var data = result.ReadFirstOrDefault<fbThread>();
        //            if (data != null)
        //            {
        //                data.Users = result.Read<fbThreadUser>().ToList();
        //                data.Customer = result.ReadFirstOrDefault<fbThreadCust>();
        //                data.Saler = data.Users.Where(u => u.userId == data.saler_userId).FirstOrDefault();
        //                data.Supporter = data.Users.Where(u => u.userId == data.supporter_userId).FirstOrDefault();
        //            }
        //            return data;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public async Task<fbThread> SetThreadInfo(string userId, fbThread thread)
        //{
        //    const string storedProcedure = "sp_Thread_Info_Set";
        //    try
        //    {
        //        using (SqlConnection connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@userId", userId);
        //            param.Add("@id", thread.id);
        //            param.Add("@region_id", thread.region_id);
        //            param.Add("@thread_name", thread.thread_name);
        //            param.Add("@thread_type", thread.thread_type);
        //            param.Add("@project_cd", thread.project_cd);
        //            param.Add("@project_name", thread.project_name);
        //            param.Add("@room_code", thread.room_code);
        //            param.Add("@sub_prod_cd", thread.sub_prod_cd);
        //            param.Add("@cust_userid", thread.cust_userId);
        //            param.Add("@saler_userId", thread.saler_userId);
        //            param.Add("@role_cd", thread.role_cd);
        //            param.AddTable("@users", "thread_user_type", thread.Users);
        //            var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //            var data = result.ReadFirst<fbThread>();
        //            if (data != null)
        //            {
        //                data.Users = result.Read<fbThreadUser>().ToList();
        //                data.Customer = result.ReadFirstOrDefault<fbThreadCust>();
        //                data.Saler = data.Users.Where(u => u.userId == data.saler_userId).FirstOrDefault();
        //                data.Supporter = data.Users.Where(u => u.userId == data.supporter_userId).FirstOrDefault();
        //                await _fbRepository.SetThreadCreate(data);
        //            }
        //            return data;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public fbThread GetThread(string userId, Guid id)
        //{
        //    const string storedProcedure = "sp_Thread_Info_Get";
        //    try
        //    {
        //        using (SqlConnection connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@UserId", userId);
        //            param.Add("@id", id);
        //            var result = connection.QueryMultiple(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //            var data = result.ReadFirstOrDefault<fbThread>();
        //            if (data != null)
        //            {
        //                data.Users = result.Read<fbThreadUser>().ToList();
        //                data.Customer = result.ReadFirstOrDefault<fbThreadCust>();
        //                data.Saler = data.Users.Where(u => u.userId == data.saler_userId).FirstOrDefault();
        //                data.Supporter = data.Users.Where(u => u.userId == data.supporter_userId).FirstOrDefault();
        //            }
        //            return data;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public async Task<BaseValidate> SetThreadInvite(string userId, Guid? id, string role, List<fbThreadUser> users)
        //{
        //    const string storedProcedure = "sp_Thread_Invite_Set";
        //    try
        //    {
        //        using (SqlConnection connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@userId", userId);
        //            param.Add("@thread_id", id);
        //            param.Add("@role", role);
        //            param.AddTable("@users", "thread_user_type", users);
        //            var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //            var reponse = result.ReadFirst<BaseValidate>();
        //            if (reponse.valid)
        //            {
        //                AppNotifyTake1 take = result.ReadFirstOrDefault<AppNotifyTake1>();
        //                if (take != null)
        //                {
        //                    take.appUsers = result.Read<PushNotifyUser>().ToList();
        //                    await _fbRepository.SetNotifyPush(take);
        //                }
        //                else
        //                {
        //                    return new BaseValidate { valid = false, messages = "Không tìm thấy thông tin" };
        //                }
        //            }
        //            return reponse;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}

        //public async Task<BaseValidate> DelThreadInvite(Guid? thread_id, string userId)
        //{
        //    const string storedProcedure = "sp_Thread_Invite_Del";
        //    try
        //    {
        //        using (SqlConnection connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@userId", userId);
        //            param.Add("@thread_id", thread_id);
        //            var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //            return result;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public async Task<BaseValidate> SetThreadUser(string userId, fbThreadUserAdd id, fbThreadUser user)
        //{
        //    const string storedProcedure = "sp_Thread_User_Set";
        //    try
        //    {
        //        using (SqlConnection connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@userId", userId);
        //            param.Add("@thread_id", id.thread_id);
        //            param.Add("@role", id.role);
        //            param.Add("@set_userId", user.userId);
        //            param.Add("@custId", user.custId);
        //            param.Add("@phone", user.phone);
        //            param.Add("@email", user.email);
        //            param.Add("@fullname", user.fullName);
        //            param.Add("@avatar", user.avatar);
        //            var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //            var reponse = result.ReadFirst<BaseValidate>();
        //            if (reponse.valid)
        //            {
        //                await _fbRepository.SetThreadUser(id, user, reponse.code);
        //                AppNotifyTake1 take = result.ReadFirstOrDefault<AppNotifyTake1>();
        //                if (take != null)
        //                {
        //                    take.appUsers = result.Read<PushNotifyUser>().ToList();
        //                    await _fbRepository.SetNotifyPush(take);
        //                }
        //            }
        //            return reponse;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public fbThreadUser GetUserApp(string userId)
        //{
        //    const string storedProcedure = "sp_COR_User_Profile_App_Get";
        //    try
        //    {
        //        using (SqlConnection connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@userId", userId);
        //            return connection.QueryFirstOrDefault<fbThreadUser>(storedProcedure, param, commandType: CommandType.StoredProcedure);

        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public async Task<BaseValidate> DelThreadUser(string userId, fbThreadUserAdd user)
        //{
        //    const string storedProcedure = "sp_Thread_User_Del";
        //    try
        //    {
        //        using (SqlConnection connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@userId", userId);
        //            param.Add("@thread_id", user.thread_id);
        //            param.Add("@role", user.role);
        //            param.Add("@del_userId", user.userId);
        //            var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //            await _fbRepository.DelThreadUser(user);
        //            return result;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}

        //#endregion thread-reg
    }
}

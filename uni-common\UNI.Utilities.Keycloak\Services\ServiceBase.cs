﻿using System.Net.Http;

namespace UNI.Utilities.Keycloak.Services
{
    public abstract class ServiceBase
    {
        protected readonly HttpClient HttpClient;
        protected readonly string? DefaultRealm;

        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="client"></param>
        protected ServiceBase(HttpClient client)
        {
            HttpClient = client;
        }

        /// <summary>
        /// ctor with default realm
        /// </summary>
        /// <param name="client"></param>
        /// <param name="defaultRealm"></param>
        protected ServiceBase(HttpClient client, string? defaultRealm)
        {
            HttpClient = client;
            DefaultRealm = defaultRealm;
        }
    }
}

namespace Uni.Personal.BLL.Interfaces
{
    /// <summary>
    /// General-purpose distributed cache service interface
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Get cached value by key
        /// </summary>
        /// <typeparam name="T">Type of cached object</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Cached value or default if not found</returns>
        Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default);

        /// <summary>
        /// Set cache value with expiration
        /// </summary>
        /// <typeparam name="T">Type of object to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        /// <param name="expiration">Cache expiration time (default: 1 hour)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task</returns>
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Remove cached value by key
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task</returns>
        Task RemoveAsync(string key, CancellationToken cancellationToken = default);

        /// <summary>
        /// Remove multiple cached values by keys
        /// </summary>
        /// <param name="keys">Cache keys to remove</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task</returns>
        Task RemoveAsync(IEnumerable<string> keys, CancellationToken cancellationToken = default);

        /// <summary>
        /// Remove cached values by pattern (supports wildcards like * and ?)
        /// </summary>
        /// <param name="pattern">Key pattern</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task</returns>
        Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);

        /// <summary>
        /// Check if key exists in cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if key exists, false otherwise</returns>
        Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get or set cached value with factory function
        /// </summary>
        /// <typeparam name="T">Type of cached object</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="factory">Factory function to create value if not cached</param>
        /// <param name="expiration">Cache expiration time (default: 1 hour)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Cached or newly created value</returns>
        Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get multiple cached values by keys
        /// </summary>
        /// <typeparam name="T">Type of cached objects</typeparam>
        /// <param name="keys">Cache keys</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Dictionary of key-value pairs (missing keys will not be included)</returns>
        Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default);

        /// <summary>
        /// Set multiple cache values with expiration
        /// </summary>
        /// <typeparam name="T">Type of objects to cache</typeparam>
        /// <param name="keyValuePairs">Dictionary of key-value pairs to cache</param>
        /// <param name="expiration">Cache expiration time (default: 1 hour)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task</returns>
        Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiration = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Refresh cache expiration time for existing key
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="expiration">New expiration time (default: 1 hour)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task</returns>
        Task RefreshAsync(string key, TimeSpan? expiration = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get all keys matching a pattern
        /// </summary>
        /// <param name="pattern">Key pattern (supports wildcards)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of matching keys</returns>
        Task<IEnumerable<string>> GetKeysAsync(string pattern = "*", CancellationToken cancellationToken = default);

        /// <summary>
        /// Clear all cache entries
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task</returns>
        Task ClearAllAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Get cache statistics (if supported by implementation)
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Cache statistics or null if not supported</returns>
        Task<CacheStatistics?> GetStatisticsAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Cache statistics information
    /// </summary>
    public class CacheStatistics
    {
        /// <summary>
        /// Total number of keys in cache
        /// </summary>
        public long TotalKeys { get; set; }

        /// <summary>
        /// Cache hit count (if available)
        /// </summary>
        public long? HitCount { get; set; }

        /// <summary>
        /// Cache miss count (if available)
        /// </summary>
        public long? MissCount { get; set; }

        /// <summary>
        /// Cache hit ratio (if available)
        /// </summary>
        public double? HitRatio => HitCount.HasValue && MissCount.HasValue && (HitCount + MissCount) > 0
            ? (double)HitCount / (HitCount + MissCount)
            : null;

        /// <summary>
        /// Memory usage in bytes (if available)
        /// </summary>
        public long? MemoryUsage { get; set; }

        /// <summary>
        /// Additional implementation-specific statistics
        /// </summary>
        public Dictionary<string, object> AdditionalStats { get; set; } = new();
    }
}

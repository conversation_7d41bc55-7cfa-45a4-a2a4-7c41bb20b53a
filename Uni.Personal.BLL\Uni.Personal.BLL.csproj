﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Uni.Personal.DAL\Uni.Personal.DAL.csproj" />
    <ProjectReference Include="..\Uni.Personal.Model\Uni.Personal.Model.csproj" />
    <ProjectReference Include="..\uni-common\UNI.Common\UNI.Common.csproj" />
    <ProjectReference Include="..\uni-common\UNI.Model\UNI.Model.csproj" />
    <ProjectReference Include="..\uni-common\UNI.Utilities.Keycloak\UNI.Utilities.Keycloak.csproj" />
  </ItemGroup>

</Project>

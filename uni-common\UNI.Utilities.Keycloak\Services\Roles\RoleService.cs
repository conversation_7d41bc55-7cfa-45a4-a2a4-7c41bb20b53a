using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using UNI.Utilities.HttpClientExtension;
using UNI.Utilities.Keycloak.Models;
using UNI.Utilities.Keycloak.Models.Users;

namespace UNI.Utilities.Keycloak.Services.Roles
{
    public class RoleService : ServiceBase, IRoleService
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="client">HTTP client</param>
        public RoleService(HttpClient client) : base(client)
        {
        }

        /// <summary>
        /// Constructor with default realm
        /// </summary>
        /// <param name="client">HTTP client</param>
        /// <param name="defaultRealm">Default realm to use when realm parameter is not provided</param>
        public RoleService(HttpClient client, string defaultRealm) : base(client, defaultRealm)
        {
        }

        #region Realm Roles

        public async Task<IEnumerable<RoleRepresentation>?> GetRealmRolesAsync(string realm, bool? briefRepresentation = null,
            int? first = null, int? max = null, string? search = null, CancellationToken cancellationToken = default)
        {
            var queryParams = new
            {
                briefRepresentation,
                first,
                max,
                search
            };

            var response = await HttpClient.GetAsync<IEnumerable<RoleRepresentation>>(
                Endpoints.RoleEndpoints.RealmRoles(realm), queryParams, cancellationToken);
            return response.Content;
        }

        public async Task<IEnumerable<RoleRepresentation>?> GetRealmRolesAsync(bool? briefRepresentation = null,
            int? first = null, int? max = null, string? search = null, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(DefaultRealm))
                throw new InvalidOperationException("Default realm is not configured. Please provide a realm parameter or configure a default realm.");

            return await GetRealmRolesAsync(DefaultRealm, briefRepresentation, first, max, search, cancellationToken);
        }

        public async Task<RoleRepresentation?> GetRealmRoleAsync(string realm, string roleName, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.GetAsync<RoleRepresentation>(
                Endpoints.RoleEndpoints.RealmRole(realm, roleName), cancellationToken);
            return response.Content;
        }

        public async Task<RoleRepresentation?> GetRealmRoleAsync(string roleName, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(DefaultRealm))
                throw new InvalidOperationException("Default realm is not configured. Please provide a realm parameter or configure a default realm.");

            return await GetRealmRoleAsync(DefaultRealm, roleName, cancellationToken);
        }

        public async Task<bool> CreateRealmRoleAsync(string realm, RoleRepresentation role, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.PostJsonAsync<string>(
                Endpoints.RoleEndpoints.RealmRoles(realm), role, cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> UpdateRealmRoleAsync(string realm, string roleName, RoleRepresentation role, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.PutJsonAsync<string>(
                Endpoints.RoleEndpoints.RealmRole(realm, roleName), role, cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> DeleteRealmRoleAsync(string realm, string roleName, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.DeleteAsync(
                Endpoints.RoleEndpoints.RealmRole(realm, roleName), cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<IEnumerable<RoleRepresentation>?> GetRealmRoleCompositesAsync(string realm, string roleName, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.GetAsync<IEnumerable<RoleRepresentation>>(
                Endpoints.RoleEndpoints.RealmRoleComposites(realm, roleName), cancellationToken);
            return response.Content;
        }

        public async Task<bool> AddRealmRoleCompositesAsync(string realm, string roleName, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.PostJsonAsync<string>(
                Endpoints.RoleEndpoints.RealmRoleComposites(realm, roleName), roles, cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> RemoveRealmRoleCompositesAsync(string realm, string roleName, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.DeleteAsync<string>(
                Endpoints.RoleEndpoints.RealmRoleComposites(realm, roleName), cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<IEnumerable<UserRepresentation>?> GetRealmRoleUsersAsync(string realm, string roleName, int? first = null, int? max = null, CancellationToken cancellationToken = default)
        {
            var queryParams = new
            {
                first,
                max
            };

            var response = await HttpClient.GetAsync<IEnumerable<UserRepresentation>>(
                Endpoints.RoleEndpoints.RealmRoleUsers(realm, roleName), queryParams, cancellationToken);
            return response.Content;
        }

        #endregion

        #region Client Roles

        public async Task<IEnumerable<RoleRepresentation>?> GetClientRolesAsync(string realm, string clientId, bool? briefRepresentation = null,
            int? first = null, int? max = null, string? search = null, CancellationToken cancellationToken = default)
        {
            var queryParams = new
            {
                briefRepresentation,
                first,
                max,
                search
            };

            var response = await HttpClient.GetAsync<IEnumerable<RoleRepresentation>>(
                Endpoints.RoleEndpoints.ClientRoles(realm, clientId), queryParams, cancellationToken);
            return response.Content;
        }

        public async Task<RoleRepresentation?> GetClientRoleAsync(string realm, string clientId, string roleName, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.GetAsync<RoleRepresentation>(
                Endpoints.RoleEndpoints.ClientRole(realm, clientId, roleName), cancellationToken);
            return response.Content;
        }

        public async Task<bool> CreateClientRoleAsync(string realm, string clientId, RoleRepresentation role, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.PostJsonAsync<string>(
                Endpoints.RoleEndpoints.ClientRoles(realm, clientId), role, cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> UpdateClientRoleAsync(string realm, string clientId, string roleName, RoleRepresentation role, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.PutJsonAsync<string>(
                Endpoints.RoleEndpoints.ClientRole(realm, clientId, roleName), role, cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> DeleteClientRoleAsync(string realm, string clientId, string roleName, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.DeleteAsync(
                Endpoints.RoleEndpoints.ClientRole(realm, clientId, roleName), cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<IEnumerable<RoleRepresentation>?> GetClientRoleCompositesAsync(string realm, string clientId, string roleName, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.GetAsync<IEnumerable<RoleRepresentation>>(
                Endpoints.RoleEndpoints.ClientRoleComposites(realm, clientId, roleName), cancellationToken);
            return response.Content;
        }

        public async Task<bool> AddClientRoleCompositesAsync(string realm, string clientId, string roleName, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.PostJsonAsync<string>(
                Endpoints.RoleEndpoints.ClientRoleComposites(realm, clientId, roleName), roles, cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> RemoveClientRoleCompositesAsync(string realm, string clientId, string roleName, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.DeleteAsync<string>(
                Endpoints.RoleEndpoints.ClientRoleComposites(realm, clientId, roleName), cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<IEnumerable<UserRepresentation>?> GetClientRoleUsersAsync(string realm, string clientId, string roleName, int? first = null, int? max = null, CancellationToken cancellationToken = default)
        {
            var queryParams = new
            {
                first,
                max
            };

            var response = await HttpClient.GetAsync<IEnumerable<UserRepresentation>>(
                Endpoints.RoleEndpoints.ClientRoleUsers(realm, clientId, roleName), queryParams, cancellationToken);
            return response.Content;
        }

        #endregion

        #region Role Mappings

        public async Task<RolesRepresentation?> GetUserRoleMappingsAsync(string realm, string userId, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.GetAsync<RolesRepresentation>(
                Endpoints.RoleEndpoints.UserRoleMappings(realm, userId), cancellationToken);
            return response.Content;
        }

        public async Task<IEnumerable<RoleRepresentation>?> GetUserRealmRoleMappingsAsync(string realm, string userId, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.GetAsync<IEnumerable<RoleRepresentation>>(
                Endpoints.RoleEndpoints.UserRealmRoleMappings(realm, userId), cancellationToken);
            return response.Content;
        }

        public async Task<bool> AddUserRealmRoleMappingsAsync(string realm, string userId, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.PostJsonAsync<string>(
                Endpoints.RoleEndpoints.UserRealmRoleMappings(realm, userId), roles, cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> RemoveUserRealmRoleMappingsAsync(string realm, string userId, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.DeleteAsync<string>(
                Endpoints.RoleEndpoints.UserRealmRoleMappings(realm, userId), cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<IEnumerable<RoleRepresentation>?> GetUserClientRoleMappingsAsync(string realm, string userId, string clientId, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.GetAsync<IEnumerable<RoleRepresentation>>(
                Endpoints.RoleEndpoints.UserClientRoleMappings(realm, userId, clientId), cancellationToken);
            return response.Content;
        }

        public async Task<bool> AddUserClientRoleMappingsAsync(string realm, string userId, string clientId, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.PostJsonAsync<string>(
                Endpoints.RoleEndpoints.UserClientRoleMappings(realm, userId, clientId), roles, cancellationToken);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> RemoveUserClientRoleMappingsAsync(string realm, string userId, string clientId, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default)
        {
            var response = await HttpClient.DeleteAsync<string>(
                Endpoints.RoleEndpoints.UserClientRoleMappings(realm, userId, clientId), cancellationToken);
            return response.IsSuccessStatusCode;
        }

        #endregion
    }
}

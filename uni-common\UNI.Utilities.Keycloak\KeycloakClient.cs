using System;
using System.Net.Http;
using UNI.Utilities.Keycloak.Models;
using UNI.Utilities.Keycloak.Services.Client;
using UNI.Utilities.Keycloak.Services.Realm;
using UNI.Utilities.Keycloak.Services.Roles;
using UNI.Utilities.Keycloak.Services.Users;

namespace UNI.Utilities.Keycloak
{

    public partial class KeycloakClient : IKeycloakClient
    {
        private readonly HttpClient _client;
        private readonly Endpoints _endPoints;

        public IRealmService Realms { get; }
        public IClientService Clients { get; }
        public IUserService Users { get; }
        public IRoleService Roles { get; }

        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="httpClient"></param>
        /// <param name="settings"></param>
        public KeycloakClient(HttpClient httpClient, Settings settings)
        {
            _client = httpClient;
            if (string.IsNullOrWhiteSpace(settings.Realm)) throw new ArgumentNullException(nameof(settings.Realm));
            _endPoints = new Endpoints(settings.Realm);
            Realms = new RealmService(httpClient, settings.Realm);
            Clients = new ClientService(httpClient, settings.Realm);
            Users = new UserService(httpClient, settings.Realm);
            Roles = new RoleService(httpClient, settings.Realm);
        }

    }
}
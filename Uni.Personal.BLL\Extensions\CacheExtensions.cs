using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.BLL.Services;
using Uni.Personal.Model.Configuration;

namespace Uni.Personal.BLL.Extensions
{
    /// <summary>
    /// Cache service extensions for dependency injection
    /// </summary>
    public static class CacheExtensions
    {
        /// <summary>
        /// Add Redis cache services to the service collection
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configuration">Configuration</param>
        /// <returns>Service collection</returns>
        public static IServiceCollection AddRedisCacheServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Bind cache configuration
            var cacheConfig = new CacheConfiguration();
            configuration.GetSection("Cache").Bind(cacheConfig);
            services.Configure<CacheConfiguration>(options => configuration.GetSection("Cache").Bind(options));

            // Add Redis connection multiplexer
            services.AddSingleton<IConnectionMultiplexer>(provider =>
            {
                var connectionString = cacheConfig.ConnectionString;
                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new InvalidOperationException("Redis connection string is not configured");
                }

                var configurationOptions = ConfigurationOptions.Parse(connectionString);
                configurationOptions.ConnectTimeout = cacheConfig.ConnectTimeoutSeconds * 1000;
                configurationOptions.SyncTimeout = cacheConfig.CommandTimeoutSeconds * 1000;
                configurationOptions.ConnectRetry = cacheConfig.MaxRetryAttempts;
                configurationOptions.ReconnectRetryPolicy = new ExponentialRetry(1000);
                configurationOptions.AbortOnConnectFail = false;

                return ConnectionMultiplexer.Connect(configurationOptions);
            });

            // Add distributed cache
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = cacheConfig.ConnectionString;
                options.InstanceName = cacheConfig.KeyPrefix;
            });

            // Add cache service
            services.AddScoped<ICacheService, RedisCacheService>();

            return services;
        }

        /// <summary>
        /// Add in-memory cache services for development/testing
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <returns>Service collection</returns>
        public static IServiceCollection AddInMemoryCacheServices(this IServiceCollection services)
        {
            services.AddMemoryCache();
            services.AddScoped<ICacheService, MemoryCacheService>();
            return services;
        }
    }
}

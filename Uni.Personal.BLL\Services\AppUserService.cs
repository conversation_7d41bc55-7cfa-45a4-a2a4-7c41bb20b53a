using Serilog;
using Uni.Personal.BLL.BusinessInterfaces.Api;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using UNI.Model;
using UNI.Model.Account;
using UNI.Model.APPM;
using UNI.Model.Core;
using UNI.Utilities.Keycloak;
using UNI.Utilities.Keycloak.Models;
using UNI.Utilities.Keycloak.Models.Users;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Class User Service.
    /// <author>duongpx</author>
    /// <date>2024/12/02</date>
    /// </summary>
    public class AppUserService : IAppUserService
    {
        private readonly IAppUserRepository _userRepository;
        private readonly IApiProfileService _apiProfileService;
        private readonly IKeycloakClient _keycloakClient;
        //private readonly IApiRocketChatService _rocketChatService;
        //private readonly IApiFireBaseService _firebaseService;

        /// <summary>
        /// Constructor for AppUserService
        /// </summary>
        /// <param name="userRepository">User repository</param>
        /// <param name="apiProfileService">API profile service</param>
        /// <param name="keycloakClient">Keycloak client</param>
        public AppUserService(
            IAppUserRepository userRepository,
            IApiProfileService apiProfileService,
            IKeycloakClient keycloakClient
            )
        {
            _userRepository = userRepository;
            _apiProfileService = apiProfileService;
            _keycloakClient = keycloakClient;
        }
        public Task<coreUserLoginResponse> SetUserRegister(coreUserLoginReg reg)
        {
            return _userRepository.SetUserRegister(reg);
        }
        public async Task<coreUserLoginResponse> GetUserRegisted(string reg_id)
        {
            return await _userRepository.GetUserRegisted(reg_id);
        }
        public async Task<userLoginRefesh?> SetVerificated(BaseCtrlClient clt, coreVerify code, coreUserLoginResponse registed)
        {
            userLoginRefesh? login = null;
            string loginSecret = Guid.NewGuid().ToString();
            var account = (await _keycloakClient.Users.GetUsersAsync(username: registed.loginName, max: 1)).FirstOrDefault();
            //user chưa tồn tại
            // tạo user trên keycloak
            if (account == null || account.Id == null)
            {
                var result = await SetCreateAuthenUser(new RegisterUserModel
                {
                    UserName = registed.loginName,
                    Phone = registed.phone,
                    Email = registed.email,
                    Password = loginSecret,
                    FullName = registed.fullName
                });
                if (result?.Id == null)
                {
                    throw new Exception($"Cannot create user {registed.loginName}");
                }
                account = result;
            }
            //thiet lap pass word tạm
            var resetPassword = new ResetPassword() { Password = loginSecret, IsTemporary = false };
            var createPass = await _keycloakClient.Users.ResetPasswordAsync(account.Id, resetPassword);
            if (createPass)
            {
                login = await _userRepository.SetVerificated(clt, code, account.Id);
                login.loginSecret = loginSecret;
            }
            return login;
        }

        public async Task<bool> SetLockUser(string userId, bool isLock)
        {
            var user = await _keycloakClient.Users.GetUserAsync(userId);
            if (user == null)
            {
                throw new Exception("User not found");
            }
            user.Enabled = isLock;
            var result = await _keycloakClient.Users.UpdateUserAsync(userId, user);
            return result;
        }

        public async Task<bool> ResetPassword(SetUserPassword passwordSet, string? userId = null)
        {

            if (string.IsNullOrWhiteSpace(userId))
            {
                var user = (await _keycloakClient.Users.GetUsersAsync(username: passwordSet.UserLogin, max: 1)).FirstOrDefault();
                userId = user?.Id;
            }
            if (string.IsNullOrWhiteSpace(userId))
            {
                throw new Exception($"User {passwordSet.UserLogin} not found");
            }
            var resetPassword = new ResetPassword() { Password = passwordSet.UserPassword, IsTemporary = false };
            var result = await _keycloakClient.Users.ResetPasswordAsync(userId, resetPassword);
            return result;
        }
        // public async Task<User> FindUserByName(string userName)
        // {
        //     // return await _apiUserService.GetUserByUserName(userName);
        // }
        public async Task<UserRepresentation?> SetCreateAuthenUser(RegisterUserModel user)
        {
            var newUser = new UserRepresentation
            {
                Username = user.UserName,
                Email = user.Email,
                LastName = user.FullName,
                Enabled = true
            };
            var rs = await _keycloakClient.Users.CreateUserAsync(newUser);
            if (rs.IsSuccessStatusCode)
            {
                return rs.Data;
            }
            else
            {
                throw new Exception(rs.StatusDescription);
            }
        }

        public async Task<userProfileSet> GetUserProfile(string loginUser)
        {
            return await _userRepository.GetUserProfile(loginUser);
        }

        public async Task<BaseValidate> SetUserProfile(string userId, UserProfileSet ureg)
        {
            var result = await _userRepository.SetUserProfile(userId, ureg);
            if (result.valid)
            {
                try
                {
                    // Fire-and-forget: chạy nền, không await
                    //_ = Task.Run(() => _firebaseService.setFirebaseProfile(userId, ureg.avatarUrl, ureg.fullName, ureg.fullName));
                    //_ = Task.Run(() => _rocketChatService.UpdateRocketchatInfoAsync(ureg));
                }
                catch (Exception ex)
                {
                    Log.Error("SetUserProfile_" + DateTime.Now.ToString() + "_" + ex.ToString());
                }

            }
            return result;
        }
        public async Task<userForegetResponse> GetUserForgetPassword(BaseCtrlClient clt, string loginName, string udid)
        {
            return await _userRepository.GetUserForgetPassword(clt, loginName, udid);
        }
        public async Task<coreUserLoginResponse> SetUserForgetPassword(BaseCtrlClient clt, userForegetSet forget)
        {
            return await _userRepository.SetUserForgetPassword(clt, forget);
        }
        public async Task<userLoginRefesh?> SetUserForgetVerificated(coreVerify code, coreUserLoginResponse registed, int user_type)
        {
            userLoginRefesh? login = null;
            string loginSecret = Guid.NewGuid().ToString();
            var user = (await _keycloakClient.Users.GetUsersAsync(username: registed.loginName, max: 1)).FirstOrDefault();
            if (user != null && user.Id != null)
            {
                //var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                var createPass = await _keycloakClient.Users.ResetPasswordAsync(user.Id, new ResetPassword() { Password = loginSecret, IsTemporary = false });
                if (createPass)
                {
                    await _userRepository.SetUserForgetVerificated(code, user_type);
                    login = new userLoginRefesh { loginName = registed.loginName, loginSecret = loginSecret, IsCreatePassword = true };
                }
            }
            return login;
        }


        public async Task<coreUserLoginResponse> SetResendCode(string reg_id)
        {
            return await _userRepository.SetResendCode(reg_id);
        }
        public async Task<bool> SetChangePassword(UserPassWordChange passwordSet)
        {
            var user = await _keycloakClient.Users.GetUserAsync(passwordSet.userId);
            if (user != null && user.Id != null)
            {
                var result = await _keycloakClient.Users.ResetPasswordAsync(user.Id, new ResetPassword() { Password = passwordSet.NewPassWord, IsTemporary = false });
                return result;
            }
            return false;
        }
        #region supper app
        public async Task<UserProfileFull> GetProfileFull(string loginName)
        {
            return await _userRepository.GetProfileFull(loginName);
        }
        public async Task<List<viewField>> GetProfileFields(string userId, string loginName)
        {
            return await _userRepository.GetProfileFields(userId, loginName);
        }
        public async Task SetProfileFields(string userId, viewField fields)
        {
            await _userRepository.SetProfileFields(userId, fields);
        }

        #region device
        public async Task<BaseValidate> SetSmartDevice(BaseCtrlClient clt, userSmartDevice device)
        {
            return await _userRepository.SetSmartDevice(clt, device);
        }
        public async Task<BaseValidate> DeleteSmartDevice(BaseCtrlClient clt, string udid)
        {
            return await _userRepository.DeleteSmartDevice(clt, udid);
        }
        public async Task<userSmartDevicePage> GetSmartDevices(FilterBase flt)
        {
            return await _userRepository.GetSmartDevices(flt);
        }
        public async Task<userOtpResponse> SetSmartDeviceConfirm(BaseCtrlClient clt, userSmartDeviceConfirm confirm)
        {
            return await _userRepository.SetSmartDeviceConfirm(clt, confirm);
        }
        public async Task<userOtpResponse> GetSmartDeviceVerify(BaseCtrlClient clt, userSmartDeviceVerify verify)
        {
            return await _userRepository.GetSmartDeviceVerify(clt, verify);
        }
        public async Task<BaseValidate> SetSmartDeviceVerificated(BaseCtrlClient clt, userSmartDeviceVerify verify, int status)
        {
            return await _userRepository.SetSmartDeviceVerificated(clt, verify, status);
        }
        #endregion device
    }
    //public class FirebaseConfig
    //{
    //    public static FirestoreDb InitializeFirestore()
    //    {
    //        // Đường dẫn đến tệp JSON key
    //        string pathToCredentials = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "noble-app-production-ad161932d060.json");

    //        // Tải tệp credentials
    //        Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", pathToCredentials);

    //        // Tạo kết nối tới Firestore
    //        FirestoreDb firestoreDb = FirestoreDb.Create("noble-app-production");
    //        Console.WriteLine("Connected to Firestore!");
    //        return firestoreDb;
    //    }
    //}
}
#endregion